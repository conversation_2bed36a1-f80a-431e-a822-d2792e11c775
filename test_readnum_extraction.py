#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试阅读量抓取功能
验证修改后的代码是否能正确获取阅读量
"""

import os
import sys
from batch_readnum_spider import BatchReadnumSpider

def test_auth_loading():
    """测试认证信息加载"""
    print("🔧 测试认证信息加载...")
    spider = BatchReadnumSpider()
    
    if spider.load_auth_info():
        print("✅ 认证信息加载成功")
        print(f"   __biz: {spider.biz}")
        print(f"   appmsg_token: {spider.appmsg_token[:20] if spider.appmsg_token else 'None'}...")
        print(f"   cookie长度: {len(spider.cookie_str) if spider.cookie_str else 0}")
        print(f"   关键headers:")
        for key in ['x-wechat-key', 'x-wechat-uin', 'exportkey']:
            if key in spider.headers:
                value = spider.headers[key]
                print(f"     {key}: {value[:20]}..." if len(value) > 20 else f"     {key}: {value}")
        return True
    else:
        print("❌ 认证信息加载失败")
        return False

def test_single_article():
    """测试单篇文章阅读量获取"""
    print("\n🔧 测试单篇文章阅读量获取...")
    spider = BatchReadnumSpider()
    
    if not spider.load_auth_info():
        print("❌ 认证信息加载失败，无法继续测试")
        return False
    
    # 获取文章列表
    articles = spider.get_article_list(begin_page=0, count=1)
    
    if not articles:
        print("❌ 未获取到文章列表")
        return False
    
    print(f"✅ 获取到 {len(articles)} 篇文章")
    
    # 测试第一篇文章的阅读量获取
    article = articles[0]
    print(f"📖 测试文章: {article['title'][:30]}...")
    
    stats = spider.extract_article_stats(article['url'])
    
    if stats:
        if stats.get('error'):
            print(f"⚠️ 获取统计数据遇到问题: {stats['error']}")
            return False
        else:
            print("✅ 成功获取统计数据:")
            print(f"   标题: {stats['title']}")
            print(f"   作者: {stats['author']}")
            print(f"   阅读量: {stats['read_count']}")
            print(f"   点赞数: {stats['like_count']}")
            print(f"   分享数: {stats['share_count']}")
            return True
    else:
        print("❌ 获取统计数据失败")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试阅读量抓取功能")
    print("="*50)
    
    # 检查必要文件
    if not os.path.exists("wechat_keys.txt"):
        print("❌ wechat_keys.txt 文件不存在")
        print("💡 请先运行抓包工具获取认证信息:")
        print("   mitmdump -s cookie_extractor.py --listen-port 8080")
        print("   然后在浏览器中访问微信公众号文章")
        return
    
    # 测试认证信息加载
    if not test_auth_loading():
        print("\n❌ 认证信息加载测试失败")
        return
    
    # 测试单篇文章阅读量获取
    if test_single_article():
        print("\n✅ 所有测试通过！阅读量抓取功能正常")
        print("\n🎉 现在可以使用批量抓取功能:")
        print("   python batch_readnum_spider.py")
    else:
        print("\n❌ 单篇文章测试失败")
        print("💡 可能的原因:")
        print("   1. 认证信息已过期，需要重新抓包")
        print("   2. 微信检测到异常访问，需要降低频率")
        print("   3. 网络连接问题")

if __name__ == "__main__":
    main()
