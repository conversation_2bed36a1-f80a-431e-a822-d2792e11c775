# proxy_interceptor.py - mitmproxy拦截器
"""
基于mitmproxy的微信请求拦截器
自动提取cookie、token等关键参数
"""

import json
import re
import os
from datetime import datetime
from mitmproxy import http
from config import WECHAT_DOMAINS, KEY_COOKIES, KEY_HEADERS, OUTPUT_FILE


class WechatProxyInterceptor:
    """微信代理拦截器"""
    
    def __init__(self):
        self.output_file = OUTPUT_FILE
        self.extracted_data = []
        self.saved_cookies = set()  # 用于去重
        self.init_output_file()
        
    def init_output_file(self):
        """初始化输出文件"""
        try:
            with open(self.output_file, "w", encoding="utf-8") as f:
                f.write("=== 自动化微信公众号参数抓取 ===\n")
                f.write(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            print(f"✅ 初始化输出文件: {self.output_file}")
        except Exception as e:
            print(f"❌ 初始化输出文件失败: {e}")
    
    def request(self, flow: http.HTTPFlow) -> None:
        """拦截请求"""
        request = flow.request
        
        # 检查是否为微信相关请求
        if self.is_wechat_request(request):
            self.extract_and_save(request)
    
    def is_wechat_request(self, request) -> bool:
        """判断是否为微信相关请求"""
        return any(domain in request.pretty_host for domain in WECHAT_DOMAINS)
    
    def extract_and_save(self, request):
        """提取并保存关键参数"""
        # 过滤掉监控请求
        if any(keyword in request.pretty_url.lower() for keyword in ["jsmonitor", "report", "ping"]):
            return
            
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        # 提取Cookie
        cookies_string = self.extract_cookies(request)
        if not cookies_string or cookies_string in self.saved_cookies:
            return
            
        # 提取Headers
        headers = self.extract_headers(request)
        
        # 提取URL参数
        url_params = self.extract_url_params(request.pretty_url)
        
        # 保存数据
        data = {
            'timestamp': timestamp,
            'url': request.pretty_url,
            'cookies': cookies_string,
            'headers': headers,
            'url_params': url_params
        }
        
        self.save_to_file(data)
        self.extracted_data.append(data)
        self.saved_cookies.add(cookies_string)
        
        print(f"🎯 抓取到新数据: {timestamp}")
        if url_params.get('__biz'):
            print(f"   __biz: {url_params['__biz'][:20]}...")
        if 'appmsg_token' in cookies_string:
            token_match = re.search(r'appmsg_token=([^;]+)', cookies_string)
            if token_match:
                print(f"   token: {token_match.group(1)[:20]}...")
    
    def extract_cookies(self, request):
        """提取关键Cookie"""
        if not request.cookies:
            return ""
            
        cookie_parts = []
        for cookie_name, cookie_value in request.cookies.items():
            # 保存关键cookie或长度较长的cookie
            if (any(key in cookie_name.lower() for key in KEY_COOKIES) or 
                len(cookie_value) > 20):
                cookie_parts.append(f"{cookie_name}={cookie_value}")
        
        return "; ".join(cookie_parts)
    
    def extract_headers(self, request):
        """提取关键Headers"""
        headers = {}
        for header_name in KEY_HEADERS:
            if header_name in request.headers:
                headers[header_name] = request.headers[header_name]
        return headers
    
    def extract_url_params(self, url):
        """提取URL中的关键参数"""
        params = {}
        
        # 提取__biz
        biz_match = re.search(r'__biz=([^&]+)', url)
        if biz_match:
            params['__biz'] = biz_match.group(1)
            
        # 提取其他关键参数
        key_params = ['mid', 'idx', 'sn', 'scene', 'srcid', 'subscene']
        for param in key_params:
            match = re.search(f'{param}=([^&]+)', url)
            if match:
                params[param] = match.group(1)
                
        return params
    
    def save_to_file(self, data):
        """保存数据到文件"""
        try:
            with open(self.output_file, "a", encoding="utf-8") as f:
                f.write(f"{'='*60}\n")
                f.write(f"time: {data['timestamp']}\n")
                f.write(f"allurl: {data['url']}\n")
                f.write(f"Cookies: {data['cookies']}\n")
                
                # 保存URL参数
                if data['url_params']:
                    f.write("URL_Params:\n")
                    for key, value in data['url_params'].items():
                        f.write(f"  {key}: {value}\n")
                
                # 保存Headers
                if data['headers']:
                    f.write("Headers:\n")
                    for key, value in data['headers'].items():
                        f.write(f"  {key}: {value}\n")
                
                f.write("\n")
        except Exception as e:
            print(f"❌ 保存数据失败: {e}")
    
    def get_latest_data(self):
        """获取最新抓取的数据"""
        if self.extracted_data:
            return self.extracted_data[-1]
        return None
    
    def get_all_data(self):
        """获取所有抓取的数据"""
        return self.extracted_data


# 创建拦截器实例供mitmproxy使用
addons = [WechatProxyInterceptor()]
