# 安装和使用指南

## 快速开始

### 1. 安装依赖

```bash
# 进入工具目录
cd auto_wechat_extractorcd 

# 安装Python依赖
pip install -r requirements.txt

# 安装Playwright浏览器
playwright install chromium
```

### 2. 验证安装

```bash
# 检查mitmproxy
mitmdump --version

# 检查playwright
python -c "from playwright.sync_api import sync_playwright; print('Playwright OK')"
```

### 3. 基本使用

```python
from auto_extractor import AutoWechatExtractor

# 创建抓取器
extractor = AutoWechatExtractor()

# 自动抓取
result = extractor.auto_extract([
    "https://mp.weixin.qq.com/s/your_article_link"
])

if result:
    print("抓取成功！")
    extractor.save_to_compatible_format()
```

## 详细安装步骤

### Windows系统

1. **安装Python 3.8+**
   ```bash
   # 检查Python版本
   python --version
   ```

2. **安装依赖包**
   ```bash
   pip install mitmproxy playwright requests pandas beautifulsoup4
   ```

3. **安装浏览器**
   ```bash
   playwright install chromium
   ```

4. **验证mitmproxy证书**
   ```bash
   # 启动mitmproxy生成证书
   mitmdump
   # 按Ctrl+C停止，证书已生成到 ~/.mitmproxy/
   ```

### Linux/macOS系统

1. **安装依赖**
   ```bash
   # Ubuntu/Debian
   sudo apt-get update
   sudo apt-get install python3-pip

   # macOS
   brew install python3
   ```

2. **安装Python包**
   ```bash
   pip3 install -r requirements.txt
   playwright install chromium
   ```

## 使用方法

### 方法1：命令行使用

```bash
cd auto_wechat_extractor
python auto_extractor.py
```

然后按提示输入微信文章链接。

### 方法2：编程使用

```python
from auto_extractor import AutoWechatExtractor

extractor = AutoWechatExtractor()

# 单个链接
result = extractor.auto_extract([
    "https://mp.weixin.qq.com/s/xxxxx"
], timeout=60)

# 多个链接
result = extractor.auto_extract([
    "https://mp.weixin.qq.com/s/xxxxx",
    "https://mp.weixin.qq.com/s/yyyyy"
], timeout=120, wait_per_article=15)
```

### 方法3：批量处理

1. 创建链接文件 `article_urls.txt`：
   ```
   https://mp.weixin.qq.com/s/link1
   https://mp.weixin.qq.com/s/link2
   https://mp.weixin.qq.com/s/link3
   ```

2. 运行批量处理：
   ```python
   python example_usage.py
   # 选择 "2. 批量处理示例"
   ```

## 配置说明

### 浏览器配置 (config.py)

```python
BROWSER_CONFIG = {
    "headless": False,      # True=无头模式，False=显示浏览器
    "slow_mo": 1000,        # 操作间隔(毫秒)
    "timeout": 30000,       # 页面超时(毫秒)
}
```

### 抓取配置

```python
EXTRACT_CONFIG = {
    "max_wait_time": 60,        # 最大等待时间
    "retry_times": 3,           # 重试次数
    "page_load_wait": 5,        # 页面加载等待
    "scroll_wait": 2,           # 滚动等待
}
```

## 故障排除

### 常见问题

1. **mitmproxy启动失败**
   ```bash
   # 检查端口占用
   netstat -an | grep 8080
   
   # 杀死占用进程
   taskkill /f /pid <PID>  # Windows
   kill -9 <PID>          # Linux/macOS
   ```

2. **浏览器启动失败**
   ```bash
   # 重新安装浏览器
   playwright install chromium --force
   ```

3. **证书错误**
   ```bash
   # 重新生成mitmproxy证书
   rm -rf ~/.mitmproxy
   mitmdump  # 重新生成
   ```

4. **权限错误**
   ```bash
   # Linux/macOS
   sudo chown -R $USER ~/.mitmproxy
   
   # Windows (以管理员身份运行)
   ```

### 调试模式

1. **启用详细日志**
   ```python
   import logging
   logging.basicConfig(level=logging.DEBUG)
   ```

2. **显示浏览器窗口**
   ```python
   # 在config.py中设置
   BROWSER_CONFIG["headless"] = False
   ```

3. **手动测试代理**
   ```bash
   # 启动代理
   mitmdump -s proxy_interceptor.py --listen-port 8080
   
   # 在另一个终端测试
   curl --proxy http://127.0.0.1:8080 http://httpbin.org/ip
   ```

## 与现有工具集成

抓取成功后，工具会生成兼容格式的 `wechat_keys.txt` 文件，可以直接用于现有的爬虫工具：

```python
# 1. 自动抓取参数
extractor = AutoWechatExtractor()
result = extractor.auto_extract(urls)

# 2. 保存兼容格式
extractor.save_to_compatible_format("../wechat_keys.txt")

# 3. 使用现有工具
from batch_readnum_spider import BatchReadnumSpider
spider = BatchReadnumSpider()
spider.batch_crawl_readnum()
```

## 性能优化

1. **无头模式**：设置 `headless=True` 提高速度
2. **减少等待时间**：调整 `page_load_wait` 和 `scroll_wait`
3. **并发处理**：可以同时运行多个实例处理不同链接
4. **缓存复用**：相同公众号的参数可以复用一段时间

## 安全注意事项

1. **代理安全**：mitmproxy会拦截所有HTTPS流量，请确保在安全环境使用
2. **证书管理**：定期清理和更新mitmproxy证书
3. **数据保护**：抓取的cookie和token包含敏感信息，请妥善保管
4. **合规使用**：请遵守相关法律法规和网站使用条款
