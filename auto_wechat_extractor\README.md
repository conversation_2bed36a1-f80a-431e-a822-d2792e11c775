# 自动化微信公众号参数抓取工具

## 架构说明

本工具采用 **mitmproxy + Playwright** 的组合方案，实现全自动化的微信公众号参数抓取：

### 核心组件

1. **mitmproxy**：真正的抓包工具，拦截HTTP/HTTPS请求
2. **Playwright**：自动化浏览器，模拟用户访问微信文章
3. **代理配置**：Playwright浏览器通过mitmproxy代理访问网页

### 数据流向

```
浏览器(Playwright) → mitmproxy代理 → 微信服务器
```

### 工作流程

1. 启动mitmproxy代理服务器（端口8080）
2. 启动Playwright浏览器，配置代理为mitmproxy
3. 自动访问指定的微信公众号文章链接
4. mitmproxy拦截并提取cookie、token等关键参数
5. 自动保存参数到文件

## 文件结构

```
auto_wechat_extractor/
├── README.md                    # 说明文档
├── requirements.txt             # 依赖包
├── auto_extractor.py           # 主程序入口
├── proxy_interceptor.py        # mitmproxy拦截器
├── browser_automation.py       # Playwright浏览器自动化
├── config.py                   # 配置文件
└── utils.py                    # 工具函数
```

## 安装依赖

```bash
pip install -r requirements.txt
playwright install chromium
```

## 使用方法

```python
from auto_extractor import AutoWechatExtractor

# 创建自动抓取器
extractor = AutoWechatExtractor()

# 自动抓取参数
result = extractor.auto_extract(
    article_urls=[
        "https://mp.weixin.qq.com/s/xxxxx",
        "https://mp.weixin.qq.com/s/yyyyy"
    ],
    timeout=60
)

if result:
    print(f"成功抓取到参数: {result}")
```

## 优势

- ✅ 完全自动化，无需手动操作
- ✅ 支持批量处理多个文章链接
- ✅ 自动处理代理配置和证书
- ✅ 智能重试和错误处理
- ✅ 兼容现有的cookie格式
