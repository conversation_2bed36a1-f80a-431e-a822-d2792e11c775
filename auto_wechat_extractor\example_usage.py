# example_usage.py - 使用示例
"""
自动化微信参数抓取工具使用示例
"""

from auto_extractor import AutoWechatExtractor


def simple_example():
    """简单使用示例"""
    print("🎯 简单使用示例")
    
    # 创建抓取器
    extractor = AutoWechatExtractor()
    
    # 示例文章链接（请替换为实际链接）
    article_urls = [
        "https://mp.weixin.qq.com/s/your_article_link_1",
        "https://mp.weixin.qq.com/s/your_article_link_2"
    ]
    
    # 执行自动抓取
    result = extractor.auto_extract(
        article_urls=article_urls,
        timeout=120,  # 2分钟超时
        wait_per_article=15  # 每篇文章等待15秒
    )
    
    if result:
        print("✅ 抓取成功！")
        print(f"获取到的数据: {result}")
        
        # 保存为兼容格式
        extractor.save_to_compatible_format()
    else:
        print("❌ 抓取失败")


def batch_example():
    """批量处理示例"""
    print("🎯 批量处理示例")
    
    # 从文件读取链接
    urls_file = "article_urls.txt"
    
    try:
        with open(urls_file, 'r', encoding='utf-8') as f:
            article_urls = [line.strip() for line in f if line.strip()]
    except FileNotFoundError:
        print(f"❌ 文件不存在: {urls_file}")
        print("请创建文件并添加微信文章链接，每行一个")
        return
    
    if not article_urls:
        print("❌ 文件中没有有效链接")
        return
    
    print(f"📋 从文件读取到 {len(article_urls)} 个链接")
    
    # 创建抓取器
    extractor = AutoWechatExtractor()
    
    # 批量处理
    result = extractor.auto_extract(
        article_urls=article_urls,
        timeout=300,  # 5分钟超时
        wait_per_article=10
    )
    
    if result:
        print("✅ 批量抓取成功！")
        extractor.save_to_compatible_format()
    else:
        print("❌ 批量抓取失败")


def custom_config_example():
    """自定义配置示例"""
    print("🎯 自定义配置示例")
    
    # 修改配置
    import config
    
    # 自定义浏览器配置
    config.BROWSER_CONFIG["headless"] = True  # 无头模式
    config.BROWSER_CONFIG["slow_mo"] = 500    # 加快操作速度
    
    # 自定义抓取配置
    config.EXTRACT_CONFIG["page_load_wait"] = 3  # 减少页面等待时间
    config.EXTRACT_CONFIG["scroll_wait"] = 1     # 减少滚动等待时间
    
    # 创建抓取器
    extractor = AutoWechatExtractor()
    
    # 示例链接
    article_urls = [
        "https://mp.weixin.qq.com/s/your_article_link"
    ]
    
    # 执行抓取
    result = extractor.auto_extract(
        article_urls=article_urls,
        timeout=60,
        wait_per_article=8
    )
    
    if result:
        print("✅ 自定义配置抓取成功！")
        extractor.save_to_compatible_format()


def integration_example():
    """与现有工具集成示例"""
    print("🎯 与现有工具集成示例")
    
    # 1. 自动抓取参数
    extractor = AutoWechatExtractor()
    
    article_urls = [
        "https://mp.weixin.qq.com/s/your_article_link"
    ]
    
    result = extractor.auto_extract(article_urls, timeout=90)
    
    if result:
        print("✅ 参数抓取成功")
        
        # 2. 保存为兼容格式
        extractor.save_to_compatible_format("../wechat_keys.txt")
        
        # 3. 调用现有的爬虫工具
        print("🚀 启动现有爬虫工具...")
        
        try:
            # 导入现有工具
            import sys
            sys.path.append('..')
            from batch_readnum_spider import BatchReadnumSpider
            
            # 使用抓取到的参数
            spider = BatchReadnumSpider()
            spider_results = spider.batch_crawl_readnum(
                max_pages=2,
                articles_per_page=10,
                days_back=7
            )
            
            if spider_results:
                print("✅ 爬虫执行成功！")
                spider.save_to_excel()
            else:
                print("❌ 爬虫执行失败")
                
        except ImportError as e:
            print(f"⚠️ 无法导入现有工具: {e}")
        except Exception as e:
            print(f"❌ 集成执行失败: {e}")
    else:
        print("❌ 参数抓取失败，无法继续")


if __name__ == '__main__':
    print("🎯 自动化微信参数抓取工具 - 使用示例")
    print("=" * 50)
    
    examples = {
        '1': ('简单使用示例', simple_example),
        '2': ('批量处理示例', batch_example),
        '3': ('自定义配置示例', custom_config_example),
        '4': ('与现有工具集成示例', integration_example)
    }
    
    print("\n请选择示例:")
    for key, (name, _) in examples.items():
        print(f"{key}. {name}")
    
    choice = input("\n请选择 (1-4): ").strip()
    
    if choice in examples:
        name, func = examples[choice]
        print(f"\n执行: {name}")
        print("-" * 30)
        func()
    else:
        print("❌ 无效选择")
