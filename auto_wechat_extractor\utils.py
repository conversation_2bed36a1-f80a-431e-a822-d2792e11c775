# utils.py - 工具函数
"""
自动化抓取工具的辅助函数
"""

import os
import re
import json
import time
import subprocess
import threading
from datetime import datetime
from config import OUTPUT_FILE, BACKUP_DIR


class ProcessManager:
    """进程管理器"""
    
    def __init__(self):
        self.processes = {}
    
    def start_mitmproxy(self, script_path, port=8080):
        """启动mitmproxy进程"""
        try:
            command = f"mitmdump -s {script_path} --listen-port {port} --set confdir=~/.mitmproxy"
            
            print(f"🚀 启动mitmproxy: {command}")
            
            process = subprocess.Popen(
                command,
                shell=True,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            self.processes['mitmproxy'] = process
            
            # 等待启动
            time.sleep(3)
            
            if process.poll() is None:
                print(f"✅ mitmproxy启动成功，PID: {process.pid}")
                return True
            else:
                stdout, stderr = process.communicate()
                print(f"❌ mitmproxy启动失败:")
                print(f"stdout: {stdout}")
                print(f"stderr: {stderr}")
                return False
                
        except Exception as e:
            print(f"❌ 启动mitmproxy失败: {e}")
            return False
    
    def stop_mitmproxy(self):
        """停止mitmproxy进程"""
        if 'mitmproxy' in self.processes:
            try:
                process = self.processes['mitmproxy']
                process.terminate()
                process.wait(timeout=10)
                print("✅ mitmproxy已停止")
                del self.processes['mitmproxy']
            except Exception as e:
                print(f"⚠️ 停止mitmproxy时出错: {e}")
    
    def cleanup(self):
        """清理所有进程"""
        for name, process in self.processes.items():
            try:
                process.terminate()
                process.wait(timeout=5)
                print(f"✅ 进程 {name} 已清理")
            except Exception as e:
                print(f"⚠️ 清理进程 {name} 时出错: {e}")
        self.processes.clear()


class DataParser:
    """数据解析器"""
    
    @staticmethod
    def parse_extracted_data(file_path=OUTPUT_FILE):
        """解析抓取的数据文件"""
        if not os.path.exists(file_path):
            print(f"❌ 文件不存在: {file_path}")
            return None
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 按分隔符分割记录
            records = content.split('=' * 60)
            
            parsed_data = []
            
            for record in records:
                if 'Cookies:' in record and 'allurl:' in record:
                    data = DataParser._parse_single_record(record)
                    if data:
                        parsed_data.append(data)
            
            print(f"✅ 解析完成，共 {len(parsed_data)} 条记录")
            return parsed_data
            
        except Exception as e:
            print(f"❌ 解析数据失败: {e}")
            return None
    
    @staticmethod
    def _parse_single_record(record):
        """解析单条记录"""
        try:
            lines = record.strip().split('\n')
            data = {}
            
            headers_section = False
            params_section = False
            
            for line in lines:
                line = line.strip()
                
                if line.startswith('time:'):
                    data['timestamp'] = line.split('time:', 1)[1].strip()
                elif line.startswith('allurl:'):
                    data['url'] = line.split('allurl:', 1)[1].strip()
                elif line.startswith('Cookies:'):
                    data['cookies'] = line.split('Cookies:', 1)[1].strip()
                elif line == 'Headers:':
                    headers_section = True
                    params_section = False
                    data['headers'] = {}
                elif line == 'URL_Params:':
                    params_section = True
                    headers_section = False
                    data['url_params'] = {}
                elif headers_section and line.startswith('  '):
                    match = re.match(r'\s+([^:]+):\s*(.+)', line)
                    if match:
                        data['headers'][match.group(1)] = match.group(2)
                elif params_section and line.startswith('  '):
                    match = re.match(r'\s+([^:]+):\s*(.+)', line)
                    if match:
                        data['url_params'][match.group(1)] = match.group(2)
            
            # 提取关键信息
            if 'cookies' in data:
                # 提取appmsg_token
                token_match = re.search(r'appmsg_token=([^;]+)', data['cookies'])
                if token_match:
                    data['appmsg_token'] = token_match.group(1)
            
            if 'url' in data:
                # 提取__biz
                biz_match = re.search(r'__biz=([^&]+)', data['url'])
                if biz_match:
                    data['biz'] = biz_match.group(1)
            
            return data if data.get('cookies') and data.get('url') else None
            
        except Exception as e:
            print(f"⚠️ 解析单条记录失败: {e}")
            return None
    
    @staticmethod
    def get_latest_valid_data(file_path=OUTPUT_FILE):
        """获取最新的有效数据"""
        parsed_data = DataParser.parse_extracted_data(file_path)
        
        if not parsed_data:
            return None
        
        # 按时间排序，获取最新的
        parsed_data.sort(key=lambda x: x.get('timestamp', ''), reverse=True)
        
        # 查找包含关键信息的记录
        for data in parsed_data:
            if (data.get('appmsg_token') and 
                data.get('biz') and 
                len(data.get('cookies', '')) > 50):
                return data
        
        return parsed_data[0] if parsed_data else None


class FileManager:
    """文件管理器"""
    
    @staticmethod
    def backup_file(file_path):
        """备份文件"""
        if not os.path.exists(file_path):
            return None
        
        try:
            # 创建备份目录
            os.makedirs(BACKUP_DIR, exist_ok=True)
            
            # 生成备份文件名
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = os.path.basename(file_path)
            backup_path = os.path.join(BACKUP_DIR, f"{timestamp}_{filename}")
            
            # 复制文件
            import shutil
            shutil.copy2(file_path, backup_path)
            
            print(f"✅ 文件已备份: {backup_path}")
            return backup_path
            
        except Exception as e:
            print(f"❌ 备份文件失败: {e}")
            return None
    
    @staticmethod
    def clean_old_backups(days=7):
        """清理旧备份文件"""
        if not os.path.exists(BACKUP_DIR):
            return
        
        try:
            import time
            current_time = time.time()
            cutoff_time = current_time - (days * 24 * 60 * 60)
            
            for filename in os.listdir(BACKUP_DIR):
                file_path = os.path.join(BACKUP_DIR, filename)
                if os.path.isfile(file_path):
                    file_time = os.path.getmtime(file_path)
                    if file_time < cutoff_time:
                        os.remove(file_path)
                        print(f"🗑️ 删除旧备份: {filename}")
                        
        except Exception as e:
            print(f"⚠️ 清理备份文件时出错: {e}")


def validate_wechat_url(url):
    """验证微信文章URL"""
    wechat_patterns = [
        r'https?://mp\.weixin\.qq\.com/s/',
        r'https?://mp\.weixin\.qq\.com/s\?',
    ]
    
    return any(re.match(pattern, url) for pattern in wechat_patterns)


def format_duration(seconds):
    """格式化时间长度"""
    if seconds < 60:
        return f"{seconds:.1f}秒"
    elif seconds < 3600:
        return f"{seconds/60:.1f}分钟"
    else:
        return f"{seconds/3600:.1f}小时"
